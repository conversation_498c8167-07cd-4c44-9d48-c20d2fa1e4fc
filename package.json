{"name": "gemini-live-call", "version": "1.0.0", "description": "A simple web app for Gemini Live Call feature", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["gemini", "live-call", "websocket", "audio"], "author": "", "license": "MIT"}