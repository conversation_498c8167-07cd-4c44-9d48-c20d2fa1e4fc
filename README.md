# Gemini Live Call Web App

A simple web application that implements real-time voice conversation with Google's Gemini 2.0 Flash model using the Gemini Live API.

## Features

- 🎤 **Real-time Voice Conversation**: Stream audio to and from Gemini AI
- 💬 **Text Chat**: Send text messages when not using voice
- 🔊 **Live Audio Playback**: Hear AI responses in real-time
- 📝 **Transcript Display**: See both user input and AI responses as text
- 🔒 **Secure API Key Management**: API key stored securely on backend

## Prerequisites

- Node.js (v14 or higher)
- A valid Google Gemini API key
- Modern web browser with microphone access

## Setup Instructions

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure Environment**
   - The `.env` file is already created with your API key
   - You can modify the PORT if needed (default: 3000)

3. **Start the Server**
   ```bash
   npm start
   ```
   
   For development with auto-restart:
   ```bash
   npm run dev
   ```

4. **Open the Application**
   - Navigate to `http://localhost:3000` in your browser
   - Allow microphone access when prompted

## How It Works

### Backend (server.js)
- **Express Server**: Serves the frontend and handles API endpoints
- **WebSocket Server**: Manages real-time communication between frontend and Gemini
- **Session Management**: Creates and manages Gemini Live API sessions
- **Audio Streaming**: Forwards audio chunks between frontend and Gemini API

### Frontend (index.html)
- **Audio Capture**: Uses `getUserMedia` to capture microphone input
- **Real-time Streaming**: Sends audio chunks via WebSocket to backend
- **Audio Playback**: Plays AI responses using Web Audio API
- **Chat Interface**: Displays conversation transcript

### Audio Flow
1. **User speaks** → Microphone captures audio
2. **Frontend** → Encodes audio and sends via WebSocket
3. **Backend** → Forwards audio to Gemini Live API
4. **Gemini** → Processes audio and returns response
5. **Backend** → Streams response back to frontend
6. **Frontend** → Plays audio response and displays text

## API Endpoints

- `GET /` - Serves the main application
- `POST /start-session` - Creates a new Gemini Live session

## WebSocket Messages

### Client → Server
- `join_session` - Join an existing session
- `audio_chunk` - Send audio data to Gemini
- `text_input` - Send text message to Gemini

### Server → Client
- `session_joined` - Confirmation of session join
- `gemini_response` - AI response (text/audio)
- `error` - Error messages

## Browser Compatibility

- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Requires HTTPS for microphone access

## Troubleshooting

1. **Microphone Access Denied**
   - Check browser permissions
   - Ensure HTTPS in production

2. **WebSocket Connection Failed**
   - Check if server is running
   - Verify firewall settings

3. **No Audio Playback**
   - Check browser audio permissions
   - Ensure speakers/headphones are connected

## Security Notes

- API key is stored server-side only
- All Gemini API calls are proxied through the backend
- Frontend never directly accesses the Gemini API

## File Structure

```
├── server.js          # Node.js backend with Express + WebSocket
├── index.html         # Frontend with audio streaming
├── package.json       # Dependencies and scripts
├── .env              # Environment variables (API key)
└── README.md         # This file
```
