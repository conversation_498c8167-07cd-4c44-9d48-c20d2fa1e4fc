<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini Live Call</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
            height: 600px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: #4f46e5;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .status {
            font-size: 14px;
            opacity: 0.9;
        }

        .chat-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8fafc;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: #4f46e5;
            color: white;
        }

        .message.ai .message-content {
            background: white;
            color: #374151;
            border: 1px solid #e5e7eb;
        }

        .controls {
            padding: 20px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
        }

        .call-button {
            background: #10b981;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .call-button:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .call-button.active {
            background: #ef4444;
        }

        .call-button.active:hover {
            background: #dc2626;
        }

        .call-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }

        .audio-visualizer {
            width: 100px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2px;
        }

        .bar {
            width: 3px;
            height: 10px;
            background: #4f46e5;
            border-radius: 2px;
            animation: pulse 1s infinite ease-in-out;
        }

        .bar:nth-child(2) { animation-delay: 0.1s; }
        .bar:nth-child(3) { animation-delay: 0.2s; }
        .bar:nth-child(4) { animation-delay: 0.3s; }
        .bar:nth-child(5) { animation-delay: 0.4s; }

        @keyframes pulse {
            0%, 40%, 100% { transform: scaleY(0.4); }
            20% { transform: scaleY(1); }
        }

        .hidden {
            display: none;
        }

        .text-input-container {
            display: flex;
            gap: 10px;
            flex: 1;
        }

        .text-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }

        .text-input:focus {
            border-color: #4f46e5;
        }

        .send-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
        }

        .send-button:hover {
            background: #4338ca;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 Gemini Live Call</h1>
            <div class="status" id="status">Ready to start conversation</div>
        </div>
        
        <div class="chat-container" id="chatContainer">
            <div class="message ai">
                <div class="message-content">
                    Hi! I'm your AI assistant. Click "Start Call" to begin our voice conversation, or type a message below.
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button class="call-button" id="callButton">
                <span id="callIcon">🎤</span>
                <span id="callText">Start Call</span>
            </button>
            
            <div class="audio-visualizer hidden" id="audioVisualizer">
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
            </div>
            
            <div class="text-input-container">
                <input type="text" class="text-input" id="textInput" placeholder="Type a message...">
                <button class="send-button" id="sendButton">Send</button>
            </div>
        </div>
    </div>

    <script>
        class GeminiLiveCall {
            constructor() {
                this.ws = null;
                this.sessionId = null;
                this.isCallActive = false;
                this.mediaRecorder = null;
                this.audioContext = null;
                this.audioQueue = [];
                this.isPlaying = false;
                
                this.initializeElements();
                this.setupEventListeners();
            }
            
            initializeElements() {
                this.callButton = document.getElementById('callButton');
                this.callIcon = document.getElementById('callIcon');
                this.callText = document.getElementById('callText');
                this.status = document.getElementById('status');
                this.chatContainer = document.getElementById('chatContainer');
                this.audioVisualizer = document.getElementById('audioVisualizer');
                this.textInput = document.getElementById('textInput');
                this.sendButton = document.getElementById('sendButton');
            }
            
            setupEventListeners() {
                this.callButton.addEventListener('click', () => this.toggleCall());
                this.sendButton.addEventListener('click', () => this.sendTextMessage());
                this.textInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.sendTextMessage();
                });
            }
            
            async toggleCall() {
                if (!this.isCallActive) {
                    await this.startCall();
                } else {
                    this.endCall();
                }
            }
            
            async startCall() {
                try {
                    this.updateStatus('Requesting microphone access...');
                    
                    // Request microphone access
                    const stream = await navigator.mediaDevices.getUserMedia({ 
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true
                        } 
                    });
                    
                    // Start session with backend
                    const response = await fetch('/start-session', { method: 'POST' });
                    const { sessionId } = await response.json();
                    this.sessionId = sessionId;
                    
                    // Connect WebSocket
                    this.connectWebSocket();
                    
                    // Setup audio recording
                    this.setupAudioRecording(stream);
                    
                    // Setup audio playback
                    this.setupAudioPlayback();
                    
                    this.isCallActive = true;
                    this.updateCallButton();
                    this.updateStatus('Call active - Speak now!');
                    this.audioVisualizer.classList.remove('hidden');
                    
                } catch (error) {
                    console.error('Error starting call:', error);
                    this.updateStatus('Error: Could not start call');
                }
            }
            
            connectWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                this.ws = new WebSocket(`${protocol}//${window.location.host}`);
                
                this.ws.onopen = () => {
                    this.ws.send(JSON.stringify({
                        type: 'join_session',
                        sessionId: this.sessionId
                    }));
                };
                
                this.ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    this.updateStatus('Connection error');
                };
            }
            
            setupAudioRecording(stream) {
                // Create MediaRecorder for audio capture
                this.mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });
                
                this.mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0 && this.ws && this.ws.readyState === WebSocket.OPEN) {
                        // Convert audio blob to base64 and send to backend
                        const reader = new FileReader();
                        reader.onload = () => {
                            const base64Audio = reader.result.split(',')[1];
                            this.ws.send(JSON.stringify({
                                type: 'audio_chunk',
                                audioData: base64Audio
                            }));
                        };
                        reader.readAsDataURL(event.data);
                    }
                };
                
                // Start recording in small chunks for real-time streaming
                this.mediaRecorder.start(100); // 100ms chunks
            }
            
            setupAudioPlayback() {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }
            
            handleWebSocketMessage(data) {
                switch (data.type) {
                    case 'session_joined':
                        console.log('Joined session:', data.sessionId);
                        break;
                        
                    case 'gemini_response':
                        this.handleGeminiResponse(data.data);
                        break;
                        
                    case 'error':
                        console.error('WebSocket error:', data.message);
                        this.updateStatus(`Error: ${data.message}`);
                        break;
                }
            }
            
            handleGeminiResponse(response) {
                // Handle text responses
                if (response.candidates && response.candidates[0] && response.candidates[0].content) {
                    const content = response.candidates[0].content;
                    if (content.parts) {
                        content.parts.forEach(part => {
                            if (part.text) {
                                this.addMessage('ai', part.text);
                            }
                        });
                    }
                }
                
                // Handle audio responses
                if (response.serverContent && response.serverContent.modelTurn) {
                    const turn = response.serverContent.modelTurn;
                    if (turn.parts) {
                        turn.parts.forEach(part => {
                            if (part.inlineData && part.inlineData.mimeType === 'audio/pcm') {
                                this.playAudioChunk(part.inlineData.data);
                            }
                        });
                    }
                }
            }
            
            async playAudioChunk(base64Audio) {
                try {
                    // Decode base64 audio data
                    const audioData = atob(base64Audio);
                    const arrayBuffer = new ArrayBuffer(audioData.length);
                    const view = new Uint8Array(arrayBuffer);
                    
                    for (let i = 0; i < audioData.length; i++) {
                        view[i] = audioData.charCodeAt(i);
                    }
                    
                    // Decode and play audio
                    const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
                    const source = this.audioContext.createBufferSource();
                    source.buffer = audioBuffer;
                    source.connect(this.audioContext.destination);
                    source.start();
                    
                } catch (error) {
                    console.error('Error playing audio:', error);
                }
            }
            
            sendTextMessage() {
                const text = this.textInput.value.trim();
                if (!text) return;
                
                this.addMessage('user', text);
                this.textInput.value = '';
                
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify({
                        type: 'text_input',
                        text: text
                    }));
                }
            }
            
            addMessage(sender, text) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = text;
                
                messageDiv.appendChild(contentDiv);
                this.chatContainer.appendChild(messageDiv);
                this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
            }
            
            endCall() {
                if (this.mediaRecorder) {
                    this.mediaRecorder.stop();
                    this.mediaRecorder = null;
                }
                
                if (this.ws) {
                    this.ws.close();
                    this.ws = null;
                }
                
                if (this.audioContext) {
                    this.audioContext.close();
                    this.audioContext = null;
                }
                
                this.isCallActive = false;
                this.sessionId = null;
                this.updateCallButton();
                this.updateStatus('Call ended');
                this.audioVisualizer.classList.add('hidden');
            }
            
            updateCallButton() {
                if (this.isCallActive) {
                    this.callButton.classList.add('active');
                    this.callIcon.textContent = '🔴';
                    this.callText.textContent = 'End Call';
                } else {
                    this.callButton.classList.remove('active');
                    this.callIcon.textContent = '🎤';
                    this.callText.textContent = 'Start Call';
                }
            }
            
            updateStatus(message) {
                this.status.textContent = message;
            }
        }
        
        // Initialize the app when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new GeminiLiveCall();
        });
    </script>
</body>
</html>
